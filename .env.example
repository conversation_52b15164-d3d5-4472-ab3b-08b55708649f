# =============================================================================
# 模型管理系统 - 环境配置示例文件
# =============================================================================
# 复制此文件为 .env 并修改相应的配置值

# -----------------------------------------------------------------------------
# 数据库配置
# -----------------------------------------------------------------------------
DB_ROOT_PASSWORD=models_root_password_2024
DB_NAME=vdb_models
DB_USER=models_user
DB_PASSWORD=models_password_2024
DB_PORT=3306

# -----------------------------------------------------------------------------
# 服务端口配置
# -----------------------------------------------------------------------------
MANAGER_PORT=20000
API_PORT=20001

# -----------------------------------------------------------------------------
# 应用配置
# -----------------------------------------------------------------------------
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-change-in-production-2024
ADMIN_PASSWORD=@Rw80827models

# -----------------------------------------------------------------------------
# 日志配置
# -----------------------------------------------------------------------------
LOG_LEVEL=INFO

# -----------------------------------------------------------------------------
# 安全配置
# -----------------------------------------------------------------------------
MAX_LOGIN_ATTEMPTS=5
LOGIN_ATTEMPT_TIMEOUT=600

# -----------------------------------------------------------------------------
# 性能配置
# -----------------------------------------------------------------------------
DB_POOL_SIZE=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600
ITEMS_PER_PAGE=20

# -----------------------------------------------------------------------------
# 功能开关
# -----------------------------------------------------------------------------
MONITORING_ENABLED=true
BACKUP_ENABLED=false
RATE_LIMIT_ENABLED=true
INIT_PLATFORMS_ENABLED=true

# -----------------------------------------------------------------------------
# AI平台配置 - DashScope (阿里云)
# 获取API密钥: https://dashscope.console.aliyun.com/
# -----------------------------------------------------------------------------
DASHSCOPE_PLATFORM_NAME=DashScope
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
DASHSCOPE_API_KEY=sk-your_dashscope_api_key_here

# -----------------------------------------------------------------------------
# AI平台配置 - DeepSeek
# 获取API密钥: https://platform.deepseek.com/
# -----------------------------------------------------------------------------
DEEPSEEK_PLATFORM_NAME=DeepSeek
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_API_KEY=sk-your_deepseek_api_key_here

# -----------------------------------------------------------------------------
# AI平台配置 - GLM (智谱AI)
# 获取API密钥: https://open.bigmodel.cn/
# -----------------------------------------------------------------------------
GLM_PLATFORM_NAME=GLM
GLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4
GLM_API_KEY=your_glm_api_key_here

# -----------------------------------------------------------------------------
# AI平台配置 - Spark (讯飞星火)
# 获取API密钥: https://console.xfyun.cn/
# -----------------------------------------------------------------------------
SPARK_PLATFORM_NAME=Spark
SPARK_BASE_URL=https://spark-api-open.xf-yun.com/v1
SPARK_API_KEY=your_spark_api_key_here

# -----------------------------------------------------------------------------
# AI平台配置 - OpenRouter
# 获取API密钥: https://openrouter.ai/
# -----------------------------------------------------------------------------
OPENROUTER_PLATFORM_NAME=OpenRouter
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_API_KEY=sk-or-your_openrouter_api_key_here

# -----------------------------------------------------------------------------
# AI平台配置 - DeepBricks
# 获取API密钥: https://www.deepbricks.ai/
# -----------------------------------------------------------------------------
DEEPBRICKS_PLATFORM_NAME=DeepBricks
DEEPBRICKS_BASE_URL=https://api.deepbricks.ai/v1
DEEPBRICKS_API_KEY=your_deepbricks_api_key_here

# =============================================================================
# 配置说明:
# 1. 将不需要的平台配置留空或删除对应的环境变量
# 2. API密钥请从对应平台的官方网站获取
# 3. 如果不想自动初始化平台，设置 INIT_PLATFORMS_ENABLED=false
# 4. 修改完成后重启 Docker 服务: docker-compose restart model-manager
# =============================================================================
