from models import db, Platform, AIModel, Application, AppModel
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy import and_, or_
import logging

logger = logging.getLogger(__name__)

class DatabaseService:
    """数据库服务层"""
    
    @staticmethod
    def safe_commit():
        """安全提交数据库事务"""
        try:
            db.session.commit()
            return True, None
        except IntegrityError as e:
            db.session.rollback()
            logger.error(f"数据库完整性错误: {str(e)}")
            return False, "数据完整性错误"
        except SQLAlchemyError as e:
            db.session.rollback()
            logger.error(f"数据库错误: {str(e)}")
            return False, "数据库操作失败"
        except Exception as e:
            db.session.rollback()
            logger.error(f"未知错误: {str(e)}")
            return False, "操作失败"

class PlatformService:
    """平台服务"""
    
    @staticmethod
    def get_all(page=1, per_page=20):
        """获取所有平台（分页）"""
        return Platform.query.paginate(
            page=page, per_page=per_page, error_out=False
        )
    
    @staticmethod
    def get_by_id(platform_id):
        """根据ID获取平台"""
        return Platform.query.get(platform_id)
    
    @staticmethod
    def get_by_name(name):
        """根据名称获取平台"""
        return Platform.query.filter_by(name=name).first()
    
    @staticmethod
    def create(name, base_url, api_key):
        """创建平台"""
        try:
            # 检查名称是否已存在
            if PlatformService.get_by_name(name):
                return False, "平台名称已存在", None
            
            platform = Platform(
                name=name,
                base_url=base_url,
                api_key=api_key
            )
            
            db.session.add(platform)
            success, error = DatabaseService.safe_commit()
            
            if success:
                logger.info(f"平台创建成功: {name}")
                return True, None, platform
            else:
                return False, error, None
                
        except Exception as e:
            logger.error(f"创建平台失败: {str(e)}")
            return False, "创建平台失败", None
    
    @staticmethod
    def update(platform_id, name, base_url, api_key):
        """更新平台"""
        try:
            platform = PlatformService.get_by_id(platform_id)
            if not platform:
                return False, "平台不存在", None
            
            # 只有当平台名称真的改变时才检查唯一性
            if name != platform.name:
                existing = Platform.query.filter(
                    Platform.name == name
                ).first()
                
                if existing:
                    return False, "平台名称已存在", None
            
            platform.name = name
            platform.base_url = base_url
            platform.api_key = api_key
            
            success, error = DatabaseService.safe_commit()
            
            if success:
                logger.info(f"平台更新成功: {name}")
                return True, None, platform
            else:
                return False, error, None
                
        except Exception as e:
            logger.error(f"更新平台失败: {str(e)}")
            return False, "更新平台失败", None
    
    @staticmethod
    def delete(platform_id):
        """删除平台"""
        try:
            platform = PlatformService.get_by_id(platform_id)
            if not platform:
                return False, "平台不存在"
            
            # 检查是否有关联的模型
            if platform.models:
                return False, "无法删除平台，存在关联的模型"
            
            db.session.delete(platform)
            success, error = DatabaseService.safe_commit()
            
            if success:
                logger.info(f"平台删除成功: {platform.name}")
                return True, None
            else:
                return False, error
                
        except Exception as e:
            logger.error(f"删除平台失败: {str(e)}")
            return False, "删除平台失败"

class ModelService:
    """模型服务"""
    
    @staticmethod
    def get_all(page=1, per_page=20, platform_id=None, visible_only=False, free_only=False):
        """获取所有模型（分页和过滤）"""
        query = AIModel.query
        
        if platform_id:
            query = query.filter_by(platform_id=platform_id)
        if visible_only:
            query = query.filter_by(is_visible_model=True)
        if free_only:
            query = query.filter_by(free=True)
        
        return query.paginate(page=page, per_page=per_page, error_out=False)
    
    @staticmethod
    def get_by_id(model_id):
        """根据ID获取模型"""
        return AIModel.query.get(model_id)
    
    @staticmethod
    def get_by_display_name(display_name):
        """根据显示名称获取模型"""
        return AIModel.query.filter_by(display_name=display_name).first()
    
    @staticmethod
    def create(display_name, internal_name, platform_id, **kwargs):
        """创建模型"""
        try:
            # 检查显示名称是否已存在
            if ModelService.get_by_display_name(display_name):
                return False, "模型显示名称已存在", None
            
            model = AIModel(
                display_name=display_name,
                internal_name=internal_name,
                platform_id=platform_id,
                input_token_price=kwargs.get('input_token_price', 0),
                output_token_price=kwargs.get('output_token_price', 0),
                input_picture_price=kwargs.get('input_picture_price', 0),
                is_visible_model=kwargs.get('is_visible_model', False),
                free=kwargs.get('free', False),
                high_price=kwargs.get('high_price', False)
            )
            
            db.session.add(model)
            success, error = DatabaseService.safe_commit()
            
            if success:
                logger.info(f"模型创建成功: {display_name}")
                return True, None, model
            else:
                return False, error, None
                
        except Exception as e:
            logger.error(f"创建模型失败: {str(e)}")
            return False, "创建模型失败", None
    
    @staticmethod
    def update(model_id, display_name, internal_name, platform_id, **kwargs):
        """更新模型"""
        try:
            model = ModelService.get_by_id(model_id)
            if not model:
                return False, "模型不存在", None
            
            # 只有当显示名称真的改变时才检查唯一性
            if display_name != model.display_name:
                existing = AIModel.query.filter(
                    AIModel.display_name == display_name
                ).first()
                
                if existing:
                    return False, "模型显示名称已存在", None
            
            model.display_name = display_name
            model.internal_name = internal_name
            model.platform_id = platform_id
            model.input_token_price = kwargs.get('input_token_price', 0)
            model.output_token_price = kwargs.get('output_token_price', 0)
            model.input_picture_price = kwargs.get('input_picture_price', 0)
            model.is_visible_model = kwargs.get('is_visible_model', False)
            model.free = kwargs.get('free', False)
            model.high_price = kwargs.get('high_price', False)
            
            success, error = DatabaseService.safe_commit()
            
            if success:
                logger.info(f"模型更新成功: {display_name}")
                return True, None, model
            else:
                return False, error, None
                
        except Exception as e:
            logger.error(f"更新模型失败: {str(e)}")
            return False, "更新模型失败", None
    
    @staticmethod
    def delete(model_id):
        """删除模型"""
        try:
            model = ModelService.get_by_id(model_id)
            if not model:
                return False, "模型不存在"
            
            # 检查是否有关联的应用模型
            if model.app_models:
                return False, "无法删除模型，存在关联的应用"
            
            db.session.delete(model)
            success, error = DatabaseService.safe_commit()
            
            if success:
                logger.info(f"模型删除成功: {model.display_name}")
                return True, None
            else:
                return False, error
                
        except Exception as e:
            logger.error(f"删除模型失败: {str(e)}")
            return False, "删除模型失败"

class ApplicationService:
    """应用服务"""

    @staticmethod
    def get_all(page=1, per_page=20):
        """获取所有应用（分页）"""
        return Application.query.filter_by(is_active=True).paginate(
            page=page, per_page=per_page, error_out=False
        )

    @staticmethod
    def get_by_id(app_id):
        """根据ID获取应用"""
        return Application.query.get(app_id)

    @staticmethod
    def get_by_name(name):
        """根据名称获取应用"""
        return Application.query.filter_by(name=name).first()

    @staticmethod
    def get_by_api_key(api_key):
        """根据API密钥获取应用"""
        return Application.query.filter_by(api_key=api_key, is_active=True).first()

    @staticmethod
    def create(name, description, api_key):
        """创建应用"""
        try:
            # 检查名称是否已存在
            if ApplicationService.get_by_name(name):
                return False, "应用名称已存在", None

            application = Application(
                name=name,
                description=description,
                api_key=api_key
            )

            db.session.add(application)
            success, error = DatabaseService.safe_commit()

            if success:
                logger.info(f"应用创建成功: {name}")
                return True, None, application
            else:
                return False, error, None

        except Exception as e:
            logger.error(f"创建应用失败: {str(e)}")
            return False, "创建应用失败", None

    @staticmethod
    def update(app_id, name, description):
        """更新应用"""
        try:
            application = ApplicationService.get_by_id(app_id)
            if not application:
                return False, "应用不存在", None

            # 只有当应用名称真的改变时才检查唯一性
            if name != application.name:
                existing = Application.query.filter(
                    Application.name == name
                ).first()

                if existing:
                    return False, "应用名称已存在", None

            application.name = name
            application.description = description

            success, error = DatabaseService.safe_commit()

            if success:
                logger.info(f"应用更新成功: {name}")
                return True, None, application
            else:
                return False, error, None

        except Exception as e:
            logger.error(f"更新应用失败: {str(e)}")
            return False, "更新应用失败", None

    @staticmethod
    def regenerate_api_key(app_id, new_api_key):
        """重新生成API密钥"""
        try:
            application = ApplicationService.get_by_id(app_id)
            if not application:
                return False, "应用不存在", None

            application.api_key = new_api_key

            success, error = DatabaseService.safe_commit()

            if success:
                logger.info(f"应用API密钥重新生成成功: {application.name}")
                return True, None, application
            else:
                return False, error, None

        except Exception as e:
            logger.error(f"重新生成API密钥失败: {str(e)}")
            return False, "重新生成API密钥失败", None

    @staticmethod
    def delete(app_id):
        """删除应用"""
        try:
            application = ApplicationService.get_by_id(app_id)
            if not application:
                return False, "应用不存在"

            # 软删除：设置为非活跃状态
            application.is_active = False

            success, error = DatabaseService.safe_commit()

            if success:
                logger.info(f"应用删除成功: {application.name}")
                return True, None
            else:
                return False, error

        except Exception as e:
            logger.error(f"删除应用失败: {str(e)}")
            return False, "删除应用失败"

class AppModelService:
    """应用模型关联服务"""

    @staticmethod
    def get_all(page=1, per_page=20):
        """获取所有应用模型关联（分页）"""
        return AppModel.query.paginate(
            page=page, per_page=per_page, error_out=False
        )

    @staticmethod
    def get_by_id(app_model_id):
        """根据ID获取应用模型关联"""
        return AppModel.query.get(app_model_id)

    @staticmethod
    def get_by_app_id(app_id):
        """根据应用ID获取所有关联模型"""
        return AppModel.query.filter_by(application_id=app_id).all()

    @staticmethod
    def get_by_app_and_model(app_id, model_id):
        """根据应用ID和模型ID获取关联"""
        return AppModel.query.filter_by(
            application_id=app_id,
            model_id=model_id
        ).first()

    @staticmethod
    def create(app_id, model_id, is_default=False):
        """创建应用模型关联"""
        try:
            # 检查关联是否已存在
            if AppModelService.get_by_app_and_model(app_id, model_id):
                return False, "该应用模型关联已存在", None

            # 如果设置为默认，先取消该应用的其他默认模型
            if is_default:
                existing_defaults = AppModel.query.filter_by(
                    application_id=app_id,
                    is_default=True
                ).all()
                for default in existing_defaults:
                    default.is_default = False

            app_model = AppModel(
                application_id=app_id,
                model_id=model_id,
                is_default=is_default
            )

            db.session.add(app_model)
            success, error = DatabaseService.safe_commit()

            if success:
                logger.info(f"应用模型关联创建成功: App {app_id} - Model {model_id}")
                return True, None, app_model
            else:
                return False, error, None

        except Exception as e:
            logger.error(f"创建应用模型关联失败: {str(e)}")
            return False, "创建应用模型关联失败", None

    @staticmethod
    def update(app_model_id, app_id, model_id, is_default=False):
        """更新应用模型关联"""
        try:
            app_model = AppModelService.get_by_id(app_model_id)
            if not app_model:
                return False, "应用模型关联不存在", None

            # 检查新的关联是否已存在（排除当前记录）
            existing = AppModel.query.filter(
                AppModel.application_id == app_id,
                AppModel.model_id == model_id,
                AppModel.id != app_model_id
            ).first()

            if existing:
                return False, "该应用模型关联已存在", None

            # 如果设置为默认，先取消该应用的其他默认模型
            if is_default and not app_model.is_default:
                existing_defaults = AppModel.query.filter_by(
                    application_id=app_id,
                    is_default=True
                ).all()
                for default in existing_defaults:
                    default.is_default = False

            app_model.application_id = app_id
            app_model.model_id = model_id
            app_model.is_default = is_default

            success, error = DatabaseService.safe_commit()

            if success:
                logger.info(f"应用模型关联更新成功: App {app_id} - Model {model_id}")
                return True, None, app_model
            else:
                return False, error, None

        except Exception as e:
            logger.error(f"更新应用模型关联失败: {str(e)}")
            return False, "更新应用模型关联失败", None

    @staticmethod
    def delete(app_model_id):
        """删除应用模型关联"""
        try:
            app_model = AppModelService.get_by_id(app_model_id)
            if not app_model:
                return False, "应用模型关联不存在"

            db.session.delete(app_model)
            success, error = DatabaseService.safe_commit()

            if success:
                logger.info(f"应用模型关联删除成功: {app_model_id}")
                return True, None
            else:
                return False, error

        except Exception as e:
            logger.error(f"删除应用模型关联失败: {str(e)}")
            return False, "删除应用模型关联失败"
