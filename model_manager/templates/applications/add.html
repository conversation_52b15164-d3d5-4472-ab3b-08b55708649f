{% extends "base.html" %}

{% block title %}添加应用 - 大语言模型管理系统{% endblock %}

{% block header %}添加应用{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('main.add_application') }}">
            {{ form.hidden_tag() }}
            <div class="mb-3">
                <label for="name" class="form-label">应用名称</label>
                <input type="text" class="form-control" id="name" name="name" required>
                <div class="form-text">应用的唯一标识名称</div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">应用描述</label>
                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                <div class="form-text">应用的简要描述</div>
            </div>

            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> API密钥将在创建应用后自动生成。
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('main.list_applications') }}" class="btn btn-secondary">返回</a>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
