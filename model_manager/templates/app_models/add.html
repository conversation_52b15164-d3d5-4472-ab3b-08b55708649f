{% extends "base.html" %}

{% block title %}添加应用模型关联 - 大语言模型管理系统{% endblock %}

{% block header %}添加应用模型关联{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('main.add_app_model') }}">
            {{ form.hidden_tag() }}
            <div class="mb-3">
                {{ form.application_id.label(class="form-label") }}
                {{ form.application_id(class="form-select") }}
                {% if form.application_id.errors %}
                    <div class="text-danger">
                        {% for error in form.application_id.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                {{ form.model_id.label(class="form-label") }}
                {{ form.model_id(class="form-select") }}
                {% if form.model_id.errors %}
                    <div class="text-danger">
                        {% for error in form.model_id.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3 form-check">
                {{ form.is_default(class="form-check-input") }}
                {{ form.is_default.label(class="form-check-label") }}
                {% if form.is_default.errors %}
                    <div class="text-danger">
                        {% for error in form.is_default.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">如果选中，此模型将成为该应用的默认模型（每个应用只能有一个默认模型）</div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('main.list_app_models') }}" class="btn btn-secondary">返回</a>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
