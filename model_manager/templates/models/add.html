{% extends "base.html" %}

{% block title %}添加模型 - 大语言模型管理系统{% endblock %}

{% block header %}添加模型{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('main.add_model') }}">
            {{ form.hidden_tag() }}

            <div class="mb-3">
                {{ form.display_name.label(class="form-label") }}
                {{ form.display_name(class="form-control") }}
                {% if form.display_name.errors %}
                    <div class="text-danger">
                        {% for error in form.display_name.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">模型的显示名称，如 GPT-4、Claude 等</div>
            </div>

            <div class="mb-3">
                {{ form.internal_name.label(class="form-label") }}
                {{ form.internal_name(class="form-control") }}
                {% if form.internal_name.errors %}
                    <div class="text-danger">
                        {% for error in form.internal_name.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">模型的内部标识，如 gpt-4、claude-2 等</div>
            </div>

            <div class="mb-3">
                {{ form.platform_id.label(class="form-label") }}
                <div class="d-flex gap-2">
                    {{ form.platform_id(class="form-select", id="platform_id") }}
                    <button type="button" class="btn btn-outline-info" id="check-account-btn" disabled>
                        <i class="bi bi-person-circle"></i> 账户信息
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="fetch-models-btn" disabled>
                        <i class="bi bi-download"></i> 获取模型
                    </button>
                </div>
                {% if form.platform_id.errors %}
                    <div class="text-danger">
                        {% for error in form.platform_id.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">选择平台后可以查看账户信息和获取可用模型列表</div>
            </div>

            <!-- 账户信息显示 -->
            <div class="mb-3" id="account-info" style="display: none;">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-info-circle"></i> 平台账户信息</h6>
                    </div>
                    <div class="card-body" id="account-details">
                        <!-- 账户信息将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- 模型选择器（从平台获取后显示） -->
            <div class="mb-3" id="model-selector" style="display: none;">
                <label class="form-label">从平台选择模型 <span class="badge bg-secondary" id="model-count"></span></label>
                <div class="input-group">
                    <input type="text" class="form-control" id="model-search" placeholder="搜索模型...">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                </div>
                <select class="form-select mt-2" id="platform-models" size="8">
                    <option value="">-- 选择模型 --</option>
                </select>
                <div class="form-text">搜索并选择模型后将自动填充下面的字段</div>
            </div>

            <div class="mb-3">
                <label for="input_token_price" class="form-label">输入Token价格</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number" class="form-control" id="input_token_price" name="input_token_price"
                           step="0.000001" min="0" value="0.000000" required>
                    <span class="input-group-text">/ 1M tokens</span>
                </div>
                <div class="form-text">每百万个输入token的价格（美元）</div>
            </div>

            <div class="mb-3">
                <label for="output_token_price" class="form-label">输出Token价格</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number" class="form-control" id="output_token_price" name="output_token_price"
                           step="0.000001" min="0" value="0.000000" required>
                    <span class="input-group-text">/ 1M tokens</span>
                </div>
                <div class="form-text">每百万个输出token的价格（美元）</div>
            </div>

            <div class="mb-3">
                <label for="input_picture_price" class="form-label">输入图片价格</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number" class="form-control" id="input_picture_price" name="input_picture_price"
                           step="0.000001" min="0" value="0.000000" required>
                    <span class="input-group-text">/ 张</span>
                </div>
                <div class="form-text">每张输入图片的价格（美元）</div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="is_visible_model" name="is_visible_model">
                <label class="form-check-label" for="is_visible_model">可见模型</label>
                <div class="form-text">是否在用户界面中显示此模型</div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="free" name="free">
                <label class="form-check-label" for="free">免费模型</label>
                <div class="form-text">是否为免费模型（不计费）</div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="high_price" name="high_price">
                <label class="form-check-label" for="high_price">高价模型</label>
                <div class="form-text">是否为高价模型（需要特殊权限）</div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('main.list_models') }}" class="btn btn-secondary">返回</a>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const platformSelect = document.getElementById('platform_id');
    const checkAccountBtn = document.getElementById('check-account-btn');
    const fetchModelsBtn = document.getElementById('fetch-models-btn');
    const accountInfo = document.getElementById('account-info');
    const accountDetails = document.getElementById('account-details');
    const modelSelector = document.getElementById('model-selector');
    const platformModelsSelect = document.getElementById('platform-models');
    const modelSearch = document.getElementById('model-search');
    const modelCount = document.getElementById('model-count');

    let allModels = []; // 存储所有模型数据

    // 监听平台选择变化
    platformSelect.addEventListener('change', function() {
        const platformId = this.value;
        if (platformId) {
            checkAccountBtn.disabled = false;
            fetchModelsBtn.disabled = false;
        } else {
            checkAccountBtn.disabled = true;
            fetchModelsBtn.disabled = true;
            accountInfo.style.display = 'none';
            modelSelector.style.display = 'none';
        }
    });

    // 检查账户信息
    checkAccountBtn.addEventListener('click', function() {
        const platformId = platformSelect.value;
        if (!platformId) {
            alert('请先选择平台');
            return;
        }

        // 显示加载状态
        const originalText = this.innerHTML;
        this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 检查中...';
        this.disabled = true;

        // 发送请求
        fetch(`/api/platform/${platformId}/account`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const account = data.account;
                    const limitRemaining = account.limit_remaining || 0;
                    const usage = account.usage || 0;
                    const limit = account.limit || 0;
                    const usagePercent = limit > 0 ? ((usage / limit) * 100).toFixed(1) : 0;

                    accountDetails.innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <strong>API密钥:</strong> ${account.label}<br>
                                <strong>账户类型:</strong> ${account.is_free_tier ? '免费账户' : '付费账户'}<br>
                                <strong>使用量:</strong> $${usage.toFixed(4)} / $${limit.toFixed(2)}
                            </div>
                            <div class="col-md-6">
                                <strong>剩余额度:</strong> $${limitRemaining.toFixed(4)}<br>
                                <strong>使用率:</strong> ${usagePercent}%<br>
                                <div class="progress mt-1">
                                    <div class="progress-bar ${usagePercent > 80 ? 'bg-danger' : usagePercent > 60 ? 'bg-warning' : 'bg-success'}"
                                         style="width: ${usagePercent}%"></div>
                                </div>
                            </div>
                        </div>
                    `;

                    accountInfo.style.display = 'block';
                    showAlert('success', '账户信息获取成功');
                } else {
                    showAlert('danger', `获取账户信息失败: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', '网络请求失败，请检查网络连接');
            })
            .finally(() => {
                // 恢复按钮状态
                this.innerHTML = originalText;
                this.disabled = false;
            });
    });

    // 获取模型列表
    fetchModelsBtn.addEventListener('click', function() {
        const platformId = platformSelect.value;
        if (!platformId) {
            alert('请先选择平台');
            return;
        }

        // 显示加载状态
        const originalText = this.innerHTML;
        this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 获取中...';
        this.disabled = true;

        // 发送请求
        fetch(`/api/platform/${platformId}/models`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 存储所有模型数据
                    allModels = data.models;

                    // 显示模型数量
                    modelCount.textContent = `${data.models.length} 个模型`;

                    // 渲染模型列表
                    renderModels(allModels);

                    // 显示模型选择器
                    modelSelector.style.display = 'block';

                    // 显示成功消息
                    showAlert('success', `成功获取到 ${data.models.length} 个模型`);
                } else {
                    showAlert('danger', `获取模型失败: ${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('danger', '网络请求失败，请检查网络连接');
            })
            .finally(() => {
                // 恢复按钮状态
                this.innerHTML = originalText;
                this.disabled = false;
            });
    });

    // 模型搜索功能
    modelSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const filteredModels = allModels.filter(model =>
            model.name.toLowerCase().includes(searchTerm) ||
            model.id.toLowerCase().includes(searchTerm) ||
            (model.provider && model.provider.toLowerCase().includes(searchTerm))
        );
        renderModels(filteredModels);
        modelCount.textContent = `${filteredModels.length} / ${allModels.length} 个模型`;
    });

    // 渲染模型列表
    function renderModels(models) {
        platformModelsSelect.innerHTML = '<option value="">-- 选择模型 --</option>';

        models.forEach(model => {
            const option = document.createElement('option');
            option.value = JSON.stringify(model);

            // 格式化显示文本
            const inputPrice = model.input_price.toFixed(6);
            const outputPrice = model.output_price.toFixed(6);
            const contextLength = model.context_length ? ` | ${(model.context_length/1000).toFixed(0)}K` : '';
            const provider = model.provider ? ` [${model.provider}]` : '';

            option.textContent = `${model.name}${provider} - $${inputPrice}/$${outputPrice}${contextLength}`;
            option.title = `ID: ${model.id}\n输入: $${inputPrice}/1M tokens\n输出: $${outputPrice}/1M tokens\n上下文: ${model.context_length}\n描述: ${model.description}`;

            platformModelsSelect.appendChild(option);
        });
    }

    // 监听模型选择
    platformModelsSelect.addEventListener('change', function() {
        const selectedValue = this.value;
        if (selectedValue) {
            try {
                const model = JSON.parse(selectedValue);

                // 自动填充表单字段
                document.getElementById('display_name').value = model.name;
                document.getElementById('internal_name').value = model.id;
                document.getElementById('input_token_price').value = model.input_price.toFixed(6);
                document.getElementById('output_token_price').value = model.output_price.toFixed(6);

                // 显示详细信息
                const detailsHtml = `
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> 已选择模型: ${model.name}</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>模型ID:</strong> ${model.id}<br>
                                <strong>提供商:</strong> ${model.provider || '未知'}<br>
                                <strong>上下文长度:</strong> ${model.context_length ? (model.context_length/1000).toFixed(0) + 'K tokens' : '未知'}
                            </div>
                            <div class="col-md-6">
                                <strong>输入价格:</strong> $${model.input_price.toFixed(6)}/1M tokens<br>
                                <strong>输出价格:</strong> $${model.output_price.toFixed(6)}/1M tokens<br>
                                <strong>内容审核:</strong> ${model.is_moderated ? '是' : '否'}
                            </div>
                        </div>
                        ${model.description ? `<div class="mt-2"><strong>描述:</strong> ${model.description}</div>` : ''}
                    </div>
                `;

                // 移除现有的模型详情
                const existingDetails = document.querySelector('.model-details');
                if (existingDetails) {
                    existingDetails.remove();
                }

                // 添加新的模型详情
                const detailsDiv = document.createElement('div');
                detailsDiv.className = 'model-details';
                detailsDiv.innerHTML = detailsHtml;
                modelSelector.appendChild(detailsDiv);

                showAlert('success', '模型信息已自动填充，请检查并调整');
            } catch (error) {
                console.error('Error parsing model data:', error);
                showAlert('danger', '解析模型数据失败');
            }
        } else {
            // 清除模型详情
            const existingDetails = document.querySelector('.model-details');
            if (existingDetails) {
                existingDetails.remove();
            }
        }
    });

    // 显示提示消息
    function showAlert(type, message) {
        // 移除现有的alert
        const existingAlert = document.querySelector('.alert-auto');
        if (existingAlert) {
            existingAlert.remove();
        }

        // 创建新的alert
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show alert-auto`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 插入到表单前面
        const form = document.querySelector('form');
        form.parentNode.insertBefore(alert, form);

        // 3秒后自动消失
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }
});

// 添加旋转动画CSS
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
