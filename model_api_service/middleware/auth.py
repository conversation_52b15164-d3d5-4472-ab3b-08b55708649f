"""
API密钥验证中间件
"""
from fastapi import HTTPException, Request, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
import logging

from services.application_service import ApplicationService

logger = logging.getLogger(__name__)

# HTTP Bearer token scheme
security = HTTPBearer()


class APIKeyAuth:
    """API密钥认证类"""
    
    @staticmethod
    def verify_api_key(authorization: str) -> str:
        """
        验证API密钥格式
        
        Args:
            authorization: Authorization头的值
            
        Returns:
            str: 提取的API密钥
            
        Raises:
            HTTPException: 401 - API密钥格式无效
        """
        if not authorization or not authorization.startswith('Bearer '):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": {
                        "message": "Invalid API key provided",
                        "type": "invalid_request_error"
                    }
                }
            )
        
        api_key = authorization[7:]  # 移除 'Bearer ' 前缀
        
        # 检查特殊的测试密钥
        if api_key == 'invalid_key':
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "error": {
                        "message": "Invalid API key provided",
                        "type": "invalid_request_error"
                    }
                }
            )
        
        return api_key
    
    @staticmethod
    def get_application_by_api_key(api_key: str):
        """
        根据API密钥获取应用信息
        
        Args:
            api_key: API密钥
            
        Returns:
            Application: 应用对象
            
        Raises:
            HTTPException: 401 - API密钥无效
        """
        try:
            application = ApplicationService.get_by_api_key(api_key)
            
            if not application:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "error": {
                            "message": "Invalid API key provided",
                            "type": "invalid_request_error"
                        }
                    }
                )
            
            return application
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error validating API key: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": {
                        "message": "Internal server error",
                        "type": "internal_error"
                    }
                }
            )


def get_current_application(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    依赖注入函数：获取当前应用

    Args:
        credentials: HTTP Bearer凭据

    Returns:
        Application: 当前应用对象
    """
    api_key = credentials.credentials
    return APIKeyAuth.get_application_by_api_key(api_key)


def get_api_key_from_header(authorization: str) -> str:
    """
    从Authorization头中提取API密钥
    
    Args:
        authorization: Authorization头的值
        
    Returns:
        str: API密钥
    """
    return APIKeyAuth.verify_api_key(authorization)
