"""
配置文件
"""
import os
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""

    # 应用基本信息
    app_name: str = "Model API Service"
    app_version: str = "1.0.0"
    debug: bool = False

    # 服务配置
    host: str = os.environ.get('HOST', '0.0.0.0')
    port: int = int(os.environ.get('PORT', '20001'))

    # 日志配置
    log_level: str = os.environ.get('LOG_LEVEL', 'INFO')
    log_file: str = "logs/model-api-service.log"

    # 数据库配置
    database_url: str = os.environ.get('DATABASE_URL',
        'mysql+pymysql://models_user:models_password@models_db:3306/vdb_models')

    # 速率限制配置
    rate_limit_enabled: bool = os.environ.get('RATE_LIMIT_ENABLED', 'true').lower() == 'true'
    rate_limit_default: int = int(os.environ.get('RATE_LIMIT_DEFAULT', '100'))
    rate_limit_window: int = int(os.environ.get('RATE_LIMIT_WINDOW', '3600'))

    # 文件上传配置
    max_content_length: int = int(os.environ.get('MAX_CONTENT_LENGTH', '16777216'))
    upload_folder: str = os.environ.get('UPLOAD_FOLDER', '/app/uploads')

    # 缓存配置
    cache_type: str = os.environ.get('CACHE_TYPE', 'simple')
    cache_default_timeout: int = int(os.environ.get('CACHE_DEFAULT_TIMEOUT', '600'))

    # 监控配置
    monitoring_enabled: bool = os.environ.get('MONITORING_ENABLED', 'false').lower() == 'true'
    metrics_endpoint: str = os.environ.get('METRICS_ENDPOINT', '/metrics')

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


# 创建设置实例
settings = Settings()

# 日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "default": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": settings.log_level,
            "formatter": "default",
            "stream": "ext://sys.stdout",
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": settings.log_level,
            "formatter": "detailed",
            "filename": settings.log_file,
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "encoding": "utf8",
        },
    },
    "loggers": {
        "": {
            "level": settings.log_level,
            "handlers": ["console", "file"],
        },
        "uvicorn": {
            "level": "INFO",
            "handlers": ["console"],
            "propagate": False,
        },
        "uvicorn.error": {
            "level": "INFO",
            "handlers": ["console"],
            "propagate": False,
        },
        "uvicorn.access": {
            "level": "INFO",
            "handlers": ["console"],
            "propagate": False,
        },
    },
}


# API文档配置
API_DOCS_CONFIG = {
    'title': settings.app_name,
    'description': """
    # Model API Service

    OpenAI兼容的大语言模型代理API服务，提供统一的模型访问接口。

    ## 🚀 核心功能

    - **OpenAI API兼容**: 完全兼容OpenAI API格式，可直接替换使用
    - **智能路由**: 根据API Key自动路由到对应的模型和平台
    - **安全认证**: 基于API Key的身份验证和权限控制
    - **模型授权**: 每个应用只能访问被授权的模型列表
    - **多平台支持**: 支持代理到各种LLM服务（OpenAI、Google、Anthropic等）
    - **多模态支持**: 支持文本+图片的多模态对话
    - **流式响应**: 完整支持Server-Sent Events (SSE)流式响应
    - **错误处理**: 严格按照OpenAI API错误格式返回错误信息

    ## 🔐 认证方式

    所有API请求都需要在请求头中包含有效的API密钥：

    ```
    Authorization: Bearer YOUR_API_KEY
    ```

    示例API密钥: `sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo`

    ## 🎯 工作原理

    1. **API密钥验证**: 验证请求中的API密钥并获取对应的应用
    2. **模型权限检查**: 根据应用获取可访问的模型列表
    3. **模型路由**: 根据请求的模型ID查找真实的模型配置
    4. **请求代理**: 将请求转发到真实的LLM服务
    5. **响应处理**: 处理响应并返回给客户端

    ## 📋 API端点

    - **GET /api/v1/models**: 获取可用模型列表
    - **POST /api/v1/chat/completions**: 创建聊天完成（支持多模态）
    - **GET /api/v1/health**: 服务健康检查

    ## 🔗 相关服务

    - **管理界面**: http://localhost:20000 (模型和应用管理)
    - **API服务**: http://localhost:20001 (当前服务)
    - **数据库**: localhost:3306 (MySQL数据存储)

    ## 📝 快速测试

    ```bash
    # 获取模型列表
    curl -H "Authorization: Bearer YOUR_API_KEY" \\
         http://localhost:20001/api/v1/models

    # 文本对话
    curl -X POST http://localhost:20001/api/v1/chat/completions \\
      -H "Content-Type: application/json" \\
      -H "Authorization: Bearer YOUR_API_KEY" \\
      -d '{"model": "gpt-4", "messages": [{"role": "user", "content": "Hello!"}]}'
    ```
    """,
    'version': settings.app_version,
    'contact': {
        'name': 'Model API Service',
        'email': '<EMAIL>',
    },
    'license_info': {
        'name': 'MIT License',
        'url': 'https://opensource.org/licenses/MIT',
    },
    'tags_metadata': [
        {
            'name': '系统监控',
            'description': '服务健康检查和状态监控相关接口'
        },
        {
            'name': '模型管理',
            'description': '模型列表查询和管理相关接口'
        },
        {
            'name': '聊天对话',
            'description': '聊天完成和对话生成相关接口'
        }
    ]
}
