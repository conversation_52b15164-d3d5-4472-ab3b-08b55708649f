from models import Model

class ModelService:
    """模型服务 - 只读操作"""

    @staticmethod
    def get_all(page=1, per_page=20, platform_id=None, visible_only=False, free_only=False):
        """获取所有模型（分页和过滤）"""
        query = Model.query

        if platform_id:
            query = query.filter_by(platform_id=platform_id)
        if visible_only:
            query = query.filter_by(is_visible_model=True)
        if free_only:
            query = query.filter_by(free=True)

        return query.paginate(page=page, per_page=per_page, error_out=False)

    @staticmethod
    def get_by_id(model_id):
        """根据ID获取模型"""
        return Model.query.get(model_id)

    @staticmethod
    def get_by_display_name(display_name):
        """根据显示名称获取模型"""
        return Model.query.filter_by(display_name=display_name).first()

    @staticmethod
    def get_all_with_pricing(page=1, per_page=20, platform_id=None, visible_only=False, free_only=False):
        """获取所有模型（分页和过滤，包含计费信息）"""
        query = Model.query

        if platform_id:
            query = query.filter_by(platform_id=platform_id)
        if visible_only:
            query = query.filter_by(is_visible_model=True)
        if free_only:
            query = query.filter_by(free=True)

        paginated_result = query.paginate(page=page, per_page=per_page, error_out=False)

        # 转换为包含计费信息的字典格式
        models_with_pricing = []
        for model in paginated_result.items:
            model_dict = model.to_dict()
            models_with_pricing.append(model_dict)

        # 返回包含计费信息的分页结果
        return {
            'items': models_with_pricing,
            'total': paginated_result.total,
            'pages': paginated_result.pages,
            'page': paginated_result.page,
            'per_page': paginated_result.per_page,
            'has_next': paginated_result.has_next,
            'has_prev': paginated_result.has_prev
        }