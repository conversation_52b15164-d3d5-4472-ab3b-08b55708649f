from sqlalchemy.orm import Session
from models import Application
from database import SessionLocal

class ApplicationService:
    """应用服务 - 只读操作"""

    @staticmethod
    def get_all(page=1, per_page=20):
        """获取所有应用（分页）"""
        db = SessionLocal()
        try:
            offset = (page - 1) * per_page
            return db.query(Application).filter_by(is_active=True).offset(offset).limit(per_page).all()
        finally:
            db.close()

    @staticmethod
    def get_by_id(app_id):
        """根据ID获取应用"""
        db = SessionLocal()
        try:
            return db.query(Application).filter(Application.id == app_id).first()
        finally:
            db.close()

    @staticmethod
    def get_by_name(name):
        """根据名称获取应用"""
        db = SessionLocal()
        try:
            return db.query(Application).filter_by(name=name).first()
        finally:
            db.close()

    @staticmethod
    def get_by_api_key(api_key):
        """根据API密钥获取应用"""
        db = SessionLocal()
        try:
            return db.query(Application).filter_by(api_key=api_key, is_active=True).first()
        finally:
            db.close()