from sqlalchemy.orm import Session, joinedload
from models import AppModel, Model
from database import SessionLocal

class AppModelService:
    """应用模型关联服务 - 只读操作"""

    @staticmethod
    def get_all(page=1, per_page=20):
        """获取所有应用模型关联（分页）"""
        db = SessionLocal()
        try:
            offset = (page - 1) * per_page
            return db.query(AppModel).offset(offset).limit(per_page).all()
        finally:
            db.close()

    @staticmethod
    def get_by_id(app_model_id):
        """根据ID获取应用模型关联"""
        db = SessionLocal()
        try:
            return db.query(AppModel).filter(AppModel.id == app_model_id).first()
        finally:
            db.close()

    @staticmethod
    def get_models_by_app_id(app_id):
        """根据应用ID获取所有关联模型"""
        db = SessionLocal()
        try:
            # 使用 joinedload 进行预加载，避免懒加载问题
            return db.query(AppModel).options(
                joinedload(AppModel.model).joinedload(Model.platform)
            ).filter_by(application_id=app_id).all()
        finally:
            db.close()

    @staticmethod
    def get_by_app_and_model(app_id, model_id):
        """根据应用ID和模型ID获取关联"""
        db = SessionLocal()
        try:
            return db.query(AppModel).filter_by(
                application_id=app_id,
                model_id=model_id
            ).first()
        finally:
            db.close()

    @staticmethod
    def get_models_with_pricing_by_app_id(app_id):
        """根据应用ID获取所有关联模型（包含计费信息）"""
        db = SessionLocal()
        try:
            app_models = db.query(AppModel).options(
                joinedload(AppModel.model).joinedload(Model.platform)
            ).filter_by(application_id=app_id).all()

            # 转换为包含计费信息的字典格式
            models_with_pricing = []
            for app_model in app_models:
                model_dict = app_model.to_dict()
                models_with_pricing.append(model_dict)

            return models_with_pricing
        finally:
            db.close()