"""
聊天服务 - 统一使用OpenAI格式处理所有AI服务
"""
from typing import Dict, List, Any, Optional, AsyncGenerator
import logging
import json
import asyncio
from openai import OpenAI
from fastapi import HTTPException, status

from services.app_model_service import AppModelService
from models import Application

logger = logging.getLogger(__name__)


class ChatService:
    """聊天服务类 - 统一使用OpenAI API格式与所有AI服务通信"""
    
    @staticmethod
    def get_available_models_for_app(application: Application) -> List[Dict[str, Any]]:
        """
        获取应用可用的模型列表
        
        Args:
            application: 应用对象
            
        Returns:
            List[Dict]: 模型列表，OpenAI API格式
        """
        try:
            # 获取应用关联的模型
            app_models = AppModelService.get_models_by_app_id(application.id)
            
            if not app_models:
                return []
            
            # 转换为 OpenAI API 格式
            models_data = []
            for app_model in app_models:
                model = app_model.model
                platform = model.platform


                models_data.append({
                    "id": model.display_name,  # 使用display_name作为客户端可见的模型ID
                    "object": "model",
                    "created": int(model.created_at.timestamp()) if model.created_at else 0,
                    "owned_by": platform.name,
                    "permission": [],
                    "root": model.display_name,  # 使用display_name
                    "parent": None,
                    # 计费信息 - 每百万token的价格（美元）
                    "pricing": {
                        "input_token_price": float(model.input_token_price),  # 输入token价格（每百万token）
                        "output_token_price": float(model.output_token_price),  # 输出token价格（每百万token）
                        "input_picture_price": float(model.input_picture_price),  # 输入图片价格
                        "currency": "USD",  # 货币单位
                        "unit": "per_1m_tokens"  # 计价单位：每百万token
                    },
                    # 模型特性
                    "features": {
                        "free": model.free,  # 是否免费
                        "high_price": model.high_price,  # 是否高价模型
                        "visible": model.is_visible_model  # 是否可见
                    },
                    # 内部使用的字段
                    "_internal_name": model.internal_name,  # 真实的模型名称
                    "_base_url": platform.base_url,
                    "_api_key": platform.api_key,
                    "_supports_multimodal": model.is_visible_model  # 是否支持多模态
                })
            
            return models_data
            
        except Exception as e:
            logger.error(f"Error getting models for application {application.id}: {e}")
            return []
    
    @staticmethod
    def find_model_by_display_name(models: List[Dict[str, Any]], display_name: str) -> Optional[Dict[str, Any]]:
        """
        根据模型display_name查找模型

        Args:
            models: 模型列表
            display_name: 模型显示名称（客户端提供的模型名）

        Returns:
            Optional[Dict]: 找到的模型，如果未找到返回None
        """
        for model in models:
            if model["id"] == display_name:  # id字段现在存储的是display_name
                return model
        return None
    
    @staticmethod
    async def create_chat_completion(
        request_data: Dict[str, Any], 
        application: Application,
        client_ip: str
    ) -> Dict[str, Any]:
        """
        创建聊天完成（非流式）
        
        Args:
            request_data: 聊天完成请求数据
            application: 应用对象
            client_ip: 客户端IP
            
        Returns:
            Dict: 聊天完成响应
        """
        # 获取应用可用的模型
        available_models = ChatService.get_available_models_for_app(application)
        
        if not available_models:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": {
                        "message": "No models available for this API key",
                        "type": "permission_denied_error"
                    }
                }
            )
        
        # 查找请求的模型（按display_name查找）
        requested_display_name = request_data.get("model")
        model_info = ChatService.find_model_by_display_name(available_models, requested_display_name)

        if not model_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": {
                        "message": f"Model '{requested_display_name}' not found or not accessible",
                        "type": "not_found_error"
                    }
                }
            )
        
        # 根据模型是否支持多模态来决定消息格式
        # is_visible_model=True 表示支持多模态（保留结构化content）
        # is_visible_model=False 表示仅支持文本（降级为纯文本）
        supports_multimodal = model_info.get("_supports_multimodal", False)

        original_messages = request_data.get("messages", []) or []
        processed_messages = []

        for i, m in enumerate(original_messages):
            role = m.get("role")
            content = m.get("content")

            if supports_multimodal:
                # 多模态模型：保留原始结构化content
                processed_messages.append({"role": role, "content": content})
            else:
                # 纯文本模型：将多模态内容降级为纯文本
                if isinstance(content, list):
                    texts = []
                    for part in content:
                        if isinstance(part, dict):
                            t = part.get("text") or part.get("content") or part.get("data")
                            if isinstance(t, str):
                                texts.append(t)
                    content = "\n".join(texts)
                if content is None:
                    content = ""
                processed_messages.append({"role": role, "content": content})

        real_request = {
            "model": model_info["_internal_name"],
            "messages": processed_messages
        }
        # 仅当提供时才附加常用参数
        for opt in ("max_tokens", "temperature", "top_p", "stop", "presence_penalty", "frequency_penalty"):
            val = request_data.get(opt)
            if val is not None:
                real_request[opt] = val

        # 调试日志：记录发送的请求，并将请求内容写入临时文件以便排查
        try:
            with open('/app/logs/chat_real_request.json', 'w') as f:
                json.dump(real_request, f, ensure_ascii=False, indent=2)
            logger.error("DEBUG: Wrote /app/logs/chat_real_request.json")
        except Exception as _e:
            logger.error(f"DEBUG: Failed to write debug file: {_e}")
        logger.error(f"DEBUG: Target URL: {model_info['_base_url']}/chat/completions, Model: {real_request['model']}")
        logger.error(f"DEBUG: Request keys: {list(real_request.keys())}")

        # 使用 OpenAI SDK（支持自定义 base_url）调用上游 /chat/completions
        try:
            client = OpenAI(
                base_url=model_info['_base_url'],
                api_key=model_info['_api_key']
            )

            # OpenAI SDK 是同步 API；在异步上下文中通过线程池执行
            def _call():
                return client.chat.completions.create(**real_request)

            resp = await asyncio.to_thread(_call)
            result = resp.model_dump()

            # 将内部模型名替换回用户请求的 display_name
            if 'model' in result:
                result['model'] = requested_display_name
            return result

        except Exception as e:
            # 统一异常为 HTTPException，保持对外兼容
            logger.error(f"OpenAI SDK error: {e}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail={
                    "error": {
                        "message": str(e),
                        "type": "api_connection_error"
                    }
                }
            )
    
    @staticmethod
    async def stream_chat_completion(
        request_data: Dict[str, Any], 
        application: Application,
        client_ip: str
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        创建聊天完成（流式）
        
        Args:
            request_data: 聊天完成请求数据
            application: 应用对象
            client_ip: 客户端IP
            
        Yields:
            Dict: 流式响应数据块
        """
        # 获取应用可用的模型
        available_models = ChatService.get_available_models_for_app(application)
        
        if not available_models:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={
                    "error": {
                        "message": "No models available for this API key",
                        "type": "permission_denied_error"
                    }
                }
            )
        
        # 查找请求的模型（按display_name查找）
        requested_display_name = request_data.get("model")
        model_info = ChatService.find_model_by_display_name(available_models, requested_display_name)

        if not model_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": {
                        "message": f"Model '{requested_display_name}' not found or not accessible",
                        "type": "not_found_error"
                    }
                }
            )
        
        # 根据模型是否支持多模态来决定消息格式（与非流式保持一致）
        supports_multimodal = model_info.get("_supports_multimodal", False)

        original_messages = request_data.get("messages", []) or []
        processed_messages = []

        for i, m in enumerate(original_messages):
            role = m.get("role")
            content = m.get("content")

            if supports_multimodal:
                # 多模态模型：保留原始结构化content
                processed_messages.append({"role": role, "content": content})
            else:
                # 纯文本模型：将多模态内容降级为纯文本
                if isinstance(content, list):
                    texts = []
                    for part in content:
                        if isinstance(part, dict):
                            t = part.get("text") or part.get("content") or part.get("data")
                            if isinstance(t, str):
                                texts.append(t)
                    content = "\n".join(texts)
                if content is None:
                    content = ""
                processed_messages.append({"role": role, "content": content})

        real_request = {
            "model": model_info["_internal_name"],
            "messages": processed_messages
        }
        # 仅当提供时才附加常用参数
        for opt in ("max_tokens", "temperature", "top_p", "stop", "presence_penalty", "frequency_penalty"):
            val = request_data.get(opt)
            if val is not None:
                real_request[opt] = val

        # 使用 OpenAI SDK 流式调用
        try:
            client = OpenAI(
                base_url=model_info['_base_url'],
                api_key=model_info['_api_key']
            )

            def _stream_call():
                return client.chat.completions.create(stream=True, **real_request)

            stream = await asyncio.to_thread(_stream_call)
            for event in stream:
                # OpenAI SDK 流式事件是对象，转 dict
                try:
                    data = event.model_dump()
                except Exception:
                    # 兼容部分 SDK 版本
                    data = json.loads(event.json()) if hasattr(event, 'json') else {}

                if 'model' in data:
                    data['model'] = requested_display_name
                yield data
                                
        except Exception as e:
            # 统一异常为 HTTPException，保持对外兼容
            logger.error(f"OpenAI SDK streaming error: {e}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail={
                    "error": {
                        "message": str(e),
                        "type": "api_connection_error"
                    }
                }
            )
