# main.py - Model API Service FastAPI应用
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging.config
import uvicorn
import os
from dotenv import load_dotenv

from config import settings, LOGGING_CONFIG, API_DOCS_CONFIG
from database import init_database, check_database_connection, get_database_info
from routers import models, chat, health

# 加载环境变量
def load_environment():
    """根据环境加载相应的环境变量文件"""
    env_file = f".env.{os.environ.get('FLASK_ENV', 'development')}"
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"Loaded environment from {env_file}")
    elif os.path.exists('.env'):
        load_dotenv('.env')
        print("Loaded environment from .env")
    else:
        print("No .env file found, using system environment variables")

load_environment()

# 配置日志
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    logger.info("Initializing database...")
    try:
        init_database()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise
    
    yield
    
    # 关闭时清理资源
    logger.info("Shutting down Model API Service...")

# 创建FastAPI应用
app = FastAPI(
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
    **API_DOCS_CONFIG
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(health.router, prefix="/api/v1")
app.include_router(models.router, prefix="/api/v1")
app.include_router(chat.router, prefix="/api/v1")

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": {
                "message": "Internal server error",
                "type": "internal_error"
            }
        }
    )

# 保留用于本地开发的启动代码
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
