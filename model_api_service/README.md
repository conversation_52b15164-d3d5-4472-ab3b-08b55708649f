# Model API Service

这是一个完全兼容 OpenAI API 格式的大语言模型代理服务，运行在 **20001端口**。

## 🚀 核心功能

- **OpenAI API 兼容**：完全兼容 OpenAI API 格式，可以直接替换 OpenAI API 使用
- **安全认证**：所有请求必须通过 `Authorization: Bearer <API_KEY>` 头进行认证
- **模型授权**：每个应用只能访问其被授权的模型列表
- **多平台支持**：支持代理到各种 LLM 服务（OpenAI、Google、Anthropic等）
- **多模态支持**：支持文本+图片的多模态对话
- **流式响应**：完全支持 Server-Sent Events (SSE) 流式响应
- **错误处理**：严格按照 OpenAI API 错误格式返回错误信息
- **精确计费**：提供详细的模型计费信息，支持每百万token级别的精确计费

## 🔗 服务信息

- **API端口**: 20001
- **管理界面**: http://localhost:20000
- **API文档**: http://localhost:20001/docs
- **健康检查**: http://localhost:20001/api/v1/health
- **数据库**: MySQL (端口3306)

## 💰 计费信息

本服务提供详细的模型计费信息，支持下游计费系统进行精确的费用计算：

### 计费单位
- **价格单位**: 每百万token（per_1m_tokens）
- **货币单位**: 美元（USD）
- **计费类型**:
  - `input_token_price`: 输入token价格
  - `output_token_price`: 输出token价格
  - `input_picture_price`: 输入图片价格

### 模型特性
- `free`: 是否为免费模型
- `high_price`: 是否为高价模型
- `visible`: 模型是否对用户可见

### 计费示例
```json
{
  "pricing": {
    "input_token_price": 10.0,    // $10.00 每百万输入token
    "output_token_price": 30.0,   // $30.00 每百万输出token
    "input_picture_price": 0.0,   // $0.00 每张输入图片
    "currency": "USD",
    "unit": "per_1m_tokens"
  }
}
```

## API 端点

### `GET /api/v1/models`

获取可用模型列表，完全兼容 OpenAI API 格式，**包含详细的计费信息**。

- **认证**: `Authorization: Bearer YOUR_API_KEY` (必需)
- **特色功能**:
  - 返回每个模型的详细计费信息（每百万token价格）
  - 包含模型特性标识（免费、高价、可见性等）
  - 支持下游计费系统精确计费
- **成功响应 (200 OK)**:
  ```json
  {
    "object": "list",
    "data": [
      {
        "id": "gpt-4-turbo",
        "object": "model",
        "created": 1677610602,
        "owned_by": "system",
        "permission": [],
        "root": "gpt-4-turbo",
        "parent": null,
        "pricing": {
          "input_token_price": 10.0,
          "output_token_price": 30.0,
          "input_picture_price": 0.0,
          "currency": "USD",
          "unit": "per_1m_tokens"
        },
        "features": {
          "free": false,
          "high_price": true,
          "visible": true
        }
      },
      {
        "id": "claude-3-opus",
        "object": "model",
        "created": 1677610602,
        "owned_by": "system",
        "permission": [],
        "root": "claude-3-opus",
        "parent": null,
        "pricing": {
          "input_token_price": 15.0,
          "output_token_price": 75.0,
          "input_picture_price": 0.0,
          "currency": "USD",
          "unit": "per_1m_tokens"
        },
        "features": {
          "free": false,
          "high_price": true,
          "visible": true
        }
      }
    ]
  }
  ```

### `POST /api/v1/chat/completions`

创建聊天补全，完全兼容 OpenAI API 格式。

- **认证**: `Authorization: Bearer YOUR_API_KEY` (必需)
- **请求体 (JSON)**:
  ```json
  {
    "model": "gpt-4-turbo",
    "messages": [
      {"role": "user", "content": "Hello, how are you?"}
    ],
    "stream": false,
    "temperature": 0.7,
    "max_tokens": 1024,
    "top_p": 1.0,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0
  }
  ```

- **成功响应 (非流式)**:
  ```json
  {
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-4-turbo",
    "choices": [
      {
        "index": 0,
        "message": {
          "role": "assistant",
          "content": "Hello! I'm doing well, thank you for asking."
        },
        "finish_reason": "stop"
      }
    ],
    "usage": {
      "prompt_tokens": 9,
      "completion_tokens": 12,
      "total_tokens": 21
    }
  }
  ```

- **流式响应**: 当 `stream: true` 时，返回 `text/event-stream` 格式的 SSE 流

### `GET /api/v1/health`

服务健康检查。

### 使用示例

#### cURL 示例

1. **获取可用模型**:
```bash
curl -X GET "http://localhost:20001/api/v1/models" \
     -H "Authorization: Bearer sk-qz68bll1cdr0vonXJCpQOvvzeBC_8XgMEecZ8-TJ-yU"
```

2. **聊天补全 (非流式)**:
```bash
curl -X POST "http://localhost:20001/api/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo" \
     -d '{
       "model": "Gemini 2.5 Flash",
       "messages": [
         {"role": "user", "content": "Hello!"}
       ],
       "temperature": 0.7,
       "max_tokens": 100
     }'
```

3. **聊天补全 (流式)**:
```bash
curl -X POST "http://localhost:20001/api/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo" \
     -d '{
       "model": "Gemini 2.5 Flash",
       "messages": [
         {"role": "user", "content": "Tell me a joke."}
       ],
       "stream": true
     }' --no-buffer
```

4. **多模态对话 (文本+图片)**:
```bash
curl -X POST "http://localhost:20001/api/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo" \
     -d '{
       "model": "Gemini 2.5 Flash",
       "messages": [
         {
           "role": "user",
           "content": [
             {"type": "text", "text": "描述这张图片"},
             {
               "type": "image_url",
               "image_url": {
                 "url": "data:image/jpeg;base64,/9j/4AAQ..."
               }
             }
           ]
         }
       ]
     }'
```

#### Python 示例

1. **使用 requests 库**:
```python
import requests

API_KEY = "sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo"
BASE_URL = "http://localhost:20001/api/v1"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

# 获取模型列表
response = requests.get(f"{BASE_URL}/models", headers=headers)
print(response.json())

# 聊天补全
payload = {
    "model": "Gemini 2.5 Flash",
    "messages": [
        {"role": "user", "content": "Hello, how are you?"}
    ],
    "temperature": 0.7,
    "max_tokens": 100
}

response = requests.post(f"{BASE_URL}/chat/completions", headers=headers, json=payload)
print(response.json())
```

2. **使用 OpenAI Python 库**:
```python
from openai import OpenAI

# 直接使用 OpenAI 库，只需要修改 base_url
client = OpenAI(
    api_key="sk-GMV6iEMhKXengI7X4bmXD8rm44NAD-hrp0eR2dzU3zo",
    base_url="http://localhost:20001/api/v1"
)

# 获取模型列表
models = client.models.list()
print(models)

# 聊天补全
completion = client.chat.completions.create(
    model="Gemini 2.5 Flash",
    messages=[
        {"role": "user", "content": "Hello, how are you?"}
    ],
    temperature=0.7,
    max_tokens=100
)

print(completion.choices[0].message.content)

# 流式聊天补全
stream = client.chat.completions.create(
    model="Gemini 2.5 Flash",
    messages=[
        {"role": "user", "content": "Tell me a story"}
    ],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")
```

## 支持的参数

本服务支持 OpenAI API 的所有标准参数：

### 聊天补全参数
- `model` (必需): 要使用的模型名称
- `messages` (必需): 消息列表
- `temperature`: 控制随机性 (0.0-2.0)
- `max_tokens`: 最大生成令牌数
- `top_p`: 核采样参数 (0.0-1.0)
- `frequency_penalty`: 频率惩罚 (-2.0-2.0)
- `presence_penalty`: 存在惩罚 (-2.0-2.0)
- `stream`: 是否流式返回
- `stop`: 停止序列
- `n`: 生成选择数量
- `logprobs`: 是否返回对数概率
- `top_logprobs`: 返回的对数概率数量
- `response_format`: 响应格式
- `tools`: 工具列表
- `tool_choice`: 工具选择策略
- `user`: 用户标识符

## 错误处理

所有错误都按照 OpenAI API 格式返回：

```json
{
  "error": {
    "message": "Error description",
    "type": "error_type",
    "param": "parameter_name",
    "code": "error_code"
  }
}
```

常见错误类型：
- `invalid_request_error`: 请求参数错误
- `authentication_error`: 认证失败
- `permission_denied_error`: 权限不足
- `not_found_error`: 资源不存在
- `rate_limit_exceeded`: 速率限制
- `api_connection_error`: 连接错误
- `timeout_error`: 超时错误
- `internal_server_error`: 服务器内部错误

## 🔧 配置信息

- **API端口**: 20001
- **管理端口**: 20000
- **数据库端口**: 3306
- **数据库**: MySQL (vdb_models)
- **依赖**: FastAPI, SQLAlchemy, HTTPX, Uvicorn

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│  Model API      │───▶│   LLM Platform  │
│                 │    │   Service       │    │  (OpenAI/etc)   │
│                 │    │  (Port 20001)   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  Model Manager  │
                       │  (Port 20000)   │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  MySQL Database │
                       │  (Port 3306)    │
                       └─────────────────┘
```

- **model_manager**: Web管理界面，管理平台、模型、应用和API密钥
- **model_api_service**: 提供 OpenAI 兼容的 API 接口
- **models_database**: MySQL数据库，存储所有配置信息
- **数据分离**: 管理平面和数据平面分离，提高安全性和可扩展性

## 📚 相关文档

- **API文档**: http://localhost:20001/docs
- **管理界面**: http://localhost:20000
- **健康检查**: http://localhost:20001/api/v1/health
