#!/bin/bash

# Model API Service 启动脚本
# 使用uvicorn启动FastAPI应用

set -e

# 设置默认值
HOST=${HOST:-0.0.0.0}
PORT=${PORT:-20001}
LOG_LEVEL=${LOG_LEVEL:-info}
WORKERS=${WORKERS:-4}
ENVIRONMENT=${ENVIRONMENT:-production}

# 确保日志级别是小写
LOG_LEVEL=$(echo "$LOG_LEVEL" | tr '[:upper:]' '[:lower:]')

echo "Starting Model API Service..."
echo "Environment: $ENVIRONMENT"
echo "Host: $HOST"
echo "Port: $PORT"
echo "Log Level: $LOG_LEVEL"
echo "Workers: $WORKERS"

# 检查数据库连接
echo "Checking database connection..."
python -c "
from database import check_database_connection
import sys
try:
    check_database_connection()
    print('Database connection successful')
except Exception as e:
    print(f'Database connection failed: {e}')
    sys.exit(1)
"

# 启动uvicorn服务器
if [ "$ENVIRONMENT" = "production" ]; then
    echo "Starting in production mode with $WORKERS workers..."
    exec uvicorn main:app \
        --host "$HOST" \
        --port "$PORT" \
        --log-level "$LOG_LEVEL" \
        --workers "$WORKERS" \
        --access-log \
        --no-use-colors \
        --loop uvloop \
        --http httptools
else
    echo "Starting in development mode..."
    exec uvicorn main:app \
        --host "$HOST" \
        --port "$PORT" \
        --log-level "$LOG_LEVEL" \
        --reload \
        --access-log \
        --use-colors
fi
