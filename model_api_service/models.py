# models.py
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Index, Numeric
from sqlalchemy.orm import relationship
from datetime import datetime
from decimal import Decimal

from database import Base

class Platform(Base):
    __tablename__ = 'platforms'
    __table_args__ = (
        Index('idx_platform_name', 'name'),
    )

    id = Column(Integer, primary_key=True)
    name = Column(String(50), unique=True, nullable=False, index=True)
    base_url = Column(String(255), nullable=False)
    api_key = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    models = relationship("Model", back_populates="platform", cascade="all, delete-orphan")

    def to_dict(self, include_sensitive=False):
        data = {
            'id': self.id,
            'name': self.name,
            'base_url': self.base_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'models_count': len(self.models) if self.models else 0
        }
        if include_sensitive:
            data['api_key'] = self.api_key
        return data

    def __repr__(self):
        return f'<Platform {self.name}>'

class Model(Base):
    __tablename__ = 'ai_models'
    __table_args__ = (
        Index('idx_model_display_name', 'display_name'),
        Index('idx_model_platform', 'platform_id'),
        Index('idx_model_visible', 'is_visible_model'),
        Index('idx_model_free', 'free'),
    )

    id = Column(Integer, primary_key=True)
    display_name = Column(String(50), unique=True, nullable=False, index=True)
    internal_name = Column(String(100), nullable=False)  # 修正为 internal_name，与数据库字段一致
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 价格字段 (每1,000,000个token的价格，单位为美元)
    input_token_price = Column(Numeric(10, 6), default=0.000000)  # 输入token价格（每百万token）
    output_token_price = Column(Numeric(10, 6), default=0.000000)  # 输出token价格（每百万token）
    input_picture_price = Column(Numeric(10, 6), default=0.000000)  # 输入图片价格
    is_visible_model = Column(Boolean, default=False, index=True)
    free = Column(Boolean, default=False, index=True)  # 是否免费
    high_price = Column(Boolean, default=False)  # 是否高价

    platform_id = Column(Integer, ForeignKey('platforms.id'), nullable=False, index=True)
    platform = relationship("Platform", back_populates="models")

    # 新增与应用关系
    app_models = relationship("AppModel", back_populates="model", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'display_name': self.display_name,
            'platform': self.platform.name if self.platform else None,
            'platform_id': self.platform_id,
            'internal_name': self.internal_name,
            'is_visible_model': self.is_visible_model,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            # 计费信息 - 统一格式
            'pricing': {
                'input_token_price': float(self.input_token_price),  # 输入token价格（每百万token）
                'output_token_price': float(self.output_token_price),  # 输出token价格（每百万token）
                'input_picture_price': float(self.input_picture_price),  # 输入图片价格
                'currency': 'USD',  # 货币单位
                'unit': 'per_1m_tokens'  # 计价单位：每百万token
            },
            # 模型特性
            'features': {
                'free': self.free,  # 是否免费
                'high_price': self.high_price,  # 是否高价模型
                'visible': self.is_visible_model  # 是否可见
            },
            'app_models_count': len(self.app_models) if self.app_models else 0
        }

    def __repr__(self):
        return f'<Model {self.display_name}>'

class Application(Base):
    __tablename__ = 'applications'
    __table_args__ = (
        Index('idx_app_name', 'name'),
        Index('idx_app_api_key', 'api_key'),
    )

    id = Column(Integer, primary_key=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text)
    api_key = Column(String(255), unique=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True, index=True)

    # 应用与模型的关系
    app_models = relationship("AppModel", back_populates="application", cascade="all, delete-orphan")

    def to_dict(self, include_sensitive=False):
        data = {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active,
            'models_count': len(self.app_models) if self.app_models else 0
        }
        if include_sensitive:
            data['api_key'] = self.api_key
        return data

    def __repr__(self):
        return f'<Application {self.name}>'

class AppModel(Base):
    __tablename__ = 'app_models'
    __table_args__ = (
        Index('idx_app_model_app', 'application_id'),
        Index('idx_app_model_model', 'model_id'),
        Index('idx_app_model_default', 'is_default'),
    )

    id = Column(Integer, primary_key=True)
    application_id = Column(Integer, ForeignKey('applications.id'), nullable=False, index=True)
    model_id = Column(Integer, ForeignKey('ai_models.id'), nullable=False, index=True)
    is_default = Column(Boolean, default=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    application = relationship("Application", back_populates="app_models")
    model = relationship("Model", back_populates="app_models")

    def to_dict(self):
        model_data = {}
        if self.model:
            model_data = {
                'model_name': self.model.display_name,
                'model_internal_name': self.model.internal_name,
                'platform': self.model.platform.name if self.model.platform else None,
                # 包含模型的计费信息
                'pricing': {
                    'input_token_price': float(self.model.input_token_price),
                    'output_token_price': float(self.model.output_token_price),
                    'input_picture_price': float(self.model.input_picture_price),
                    'currency': 'USD',
                    'unit': 'per_1m_tokens'  # 计价单位：每百万token
                },
                'features': {
                    'free': self.model.free,
                    'high_price': self.model.high_price,
                    'visible': self.model.is_visible_model
                }
            }

        return {
            'id': self.id,
            'application_id': self.application_id,
            'application_name': self.application.name if self.application else None,
            'model_id': self.model_id,
            'is_default': self.is_default,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            **model_data  # 展开模型数据，包含计费信息
        }

    def __repr__(self):
        return f'<AppModel {self.application.name if self.application else "Unknown"} - {self.model.display_name if self.model else "Unknown"}>'
