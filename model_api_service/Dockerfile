FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 创建必要的目录
RUN mkdir -p logs uploads

# 复制应用代码
COPY . .

# 设置启动脚本权限
RUN chmod +x start.sh

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV SERVICE_TYPE=model-api-service
ENV HOST=0.0.0.0
ENV PORT=20001
ENV LOG_LEVEL=info
ENV WORKERS=4

# 暴露端口
EXPOSE 20001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:20001/api/v1/health || exit 1

# 启动应用
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "20001", "--workers", "4", "--log-level", "info", "--access-log"]
