"""
数据库连接和配置
"""
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
import logging
from typing import Generator, Dict, Any

from config import settings

logger = logging.getLogger(__name__)

# 创建数据库引擎
engine = create_engine(
    settings.database_url,
    pool_size=10,
    pool_timeout=20,
    pool_recycle=-1,
    pool_pre_ping=True,
    echo=settings.debug
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_database():
    """初始化数据库"""
    try:
        # 导入所有模型以确保它们被注册
        from models import Platform, Model, Application, AppModel
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


def check_database_connection() -> bool:
    """检查数据库连接"""
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        return True
    except SQLAlchemyError as e:
        logger.error(f"Database connection failed: {e}")
        return False


def get_database_info() -> Dict[str, Any]:
    """获取数据库信息"""
    try:
        with engine.connect() as connection:
            # 获取数据库版本
            result = connection.execute(text("SELECT VERSION()"))
            version = result.scalar()
            
            # 获取数据库名称
            result = connection.execute(text("SELECT DATABASE()"))
            database_name = result.scalar()
            
            return {
                "status": "connected",
                "version": version,
                "database": database_name,
                "url": settings.database_url.split('@')[1] if '@' in settings.database_url else "hidden"
            }
    except SQLAlchemyError as e:
        logger.error(f"Failed to get database info: {e}")
        return {
            "status": "error",
            "error": str(e)
        }
