"""
健康检查路由
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
import logging

from database import get_db, check_database_connection, get_database_info
from config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get(
    "/health",
    summary="服务健康检查",
    description="""
    检查Model API Service的运行状态和数据库连接状态。

    **功能说明：**
    - 检查服务是否正常运行
    - 验证数据库连接状态
    - 返回服务基本信息和版本

    **返回状态：**
    - `healthy`: 服务和数据库都正常
    - `unhealthy`: 服务或数据库存在问题

    **使用场景：**
    - 监控系统健康状态
    - 负载均衡器健康检查
    - 服务部署验证
    """,
    response_description="服务健康状态信息",
    tags=["系统监控"]
)
async def health_check(db: Session = Depends(get_db)):
    """
    服务健康检查端点

    检查Model API Service的整体健康状态，包括：
    - 服务运行状态
    - 数据库连接状态
    - 服务版本信息
    """
    try:
        # 检查数据库连接
        db_status = check_database_connection()
        db_info = get_database_info()

        return {
            "status": "healthy" if db_status else "unhealthy",
            "service": settings.app_name,
            "version": settings.app_version,
            "database": db_info
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "service": settings.app_name,
            "error": str(e)
        }
