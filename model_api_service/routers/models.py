"""
模型管理路由
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from database import get_db
from middleware.auth import get_current_application
from services.chat_service import ChatService
from models import Application

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get(
    "/models",
    summary="获取可用模型列表",
    description="""
    获取当前API密钥有权访问的模型列表，完全兼容OpenAI API格式。

    **功能说明：**
    - 根据API密钥验证应用身份
    - 返回应用有权访问的模型列表
    - 响应格式完全兼容OpenAI API
    - 返回模型的显示名称（display_name）作为模型ID

    **认证要求：**
    - 必须在请求头中提供有效的API密钥
    - 格式：`Authorization: Bearer YOUR_API_KEY`

    **权限控制：**
    - 每个应用只能访问被授权的模型
    - 不同API密钥可能有不同的模型访问权限
    - 基于应用-模型关联关系进行权限控制

    **响应格式：**
    - 符合OpenAI API标准格式
    - 模型ID字段返回显示名称（如"GPT-4"、"Claude-3-Sonnet"）
    - 包含模型创建时间、所有者等信息
    - **新增计费信息**：包含每百万token的输入和输出单价（美元）
    - **新增模型特性**：免费标识、高价标识、可见性等
    - 隐藏内部实现细节（如internal_name、平台凭据等）

    **使用场景：**
    - 客户端获取可用模型列表
    - 动态构建模型选择界面
    - 验证模型可用性
    """,
    response_description="可用模型列表，OpenAI API兼容格式",
    tags=["模型管理"]
)
async def get_available_models(
    application: Application = Depends(get_current_application),
    db: Session = Depends(get_db)
):
    """
    获取可用模型列表

    根据提供的API密钥，返回应用有权访问的模型列表。
    响应格式完全兼容OpenAI API，可以直接替换OpenAI的models端点使用。

    返回的模型ID为显示名称（display_name），客户端可以直接使用这些名称
    在chat/completions API中指定模型。系统内部会自动映射到对应的
    internal_name和平台凭据。

    Args:
        application: 通过中间件验证的应用对象
        db: 数据库会话

    Returns:
        dict: 包含模型列表的字典，格式兼容OpenAI API
              模型ID字段为display_name（如"GPT-4"、"Claude-3-Sonnet"）
              包含计费信息：每百万token的输入/输出单价（美元）
              包含模型特性：免费标识、高价标识等

    Raises:
        HTTPException: 401 - API密钥无效或未提供
        HTTPException: 500 - 服务器内部错误
    """
    try:
        # 获取应用可用的模型列表
        models_data = ChatService.get_available_models_for_app(application)

        # 移除内部字段，返回包含计费信息的OpenAI API兼容格式数据
        clean_models_data = []
        for model in models_data:
            clean_model = {
                "id": model["id"],
                "object": model["object"],
                "created": model["created"],
                "owned_by": model["owned_by"],
                "permission": model["permission"],
                "root": model["root"],
                "parent": model["parent"],
                # 添加计费信息用于下游计费系统
                "pricing": model["pricing"],
                "features": model["features"]
            }
            clean_models_data.append(clean_model)

        return {
            "object": "list",
            "data": clean_models_data
        }

    except Exception as e:
        logger.error(f"Error getting models: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": {
                    "message": "Internal server error",
                    "type": "internal_error"
                }
            }
        )
