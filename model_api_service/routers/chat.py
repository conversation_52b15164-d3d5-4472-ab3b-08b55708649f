"""
聊天对话路由
"""
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
import logging
import json

from database import get_db
from middleware.auth import get_current_application
from services.chat_service import ChatService
from models import Application

logger = logging.getLogger(__name__)

router = APIRouter()


class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(..., description="消息角色: system, user, assistant")
    content: Union[str, List[Dict[str, Any]]] = Field(..., description="消息内容")
    name: Optional[str] = Field(None, description="消息发送者名称")


class ChatCompletionRequest(BaseModel):
    """聊天完成请求模型"""
    model: str = Field(..., description="模型名称")
    messages: List[ChatMessage] = Field(..., description="对话消息列表")
    temperature: Optional[float] = Field(1.0, ge=0, le=2, description="温度参数")
    top_p: Optional[float] = Field(1.0, ge=0, le=1, description="top_p参数")
    n: Optional[int] = Field(1, ge=1, le=10, description="生成数量")
    stream: Optional[bool] = Field(False, description="是否流式输出")
    stop: Optional[Union[str, List[str]]] = Field(None, description="停止词")
    max_tokens: Optional[int] = Field(None, ge=1, description="最大token数")
    presence_penalty: Optional[float] = Field(0, ge=-2, le=2, description="存在惩罚")
    frequency_penalty: Optional[float] = Field(0, ge=-2, le=2, description="频率惩罚")
    logit_bias: Optional[Dict[str, float]] = Field(None, description="logit偏置")
    user: Optional[str] = Field(None, description="用户标识")

@router.post(
    "/chat/completions",
    summary="创建聊天完成",
    description="""
    创建聊天完成请求，完全兼容OpenAI Chat Completions API。

    **功能说明：**
    - 支持多轮对话和单轮问答
    - 完全兼容OpenAI API格式和参数
    - 支持流式和非流式响应
    - 自动代理到后端LLM服务
    - 基于应用API密钥和模型显示名称的安全代理机制

    **认证要求：**
    - 必须在请求头中提供有效的API密钥
    - 格式：`Authorization: Bearer YOUR_API_KEY`
    - API密钥用于验证应用身份和获取可用模型列表

    **模型访问机制：**
    - 客户端提供模型的显示名称（display_name）
    - 系统验证API密钥确定应用身份
    - 在应用关联的模型中查找匹配的显示名称
    - 使用模型的内部名称（internal_name）访问真实的LLM服务
    - 使用模型关联平台的base_url和api_key进行代理请求
    - 避免将服务商密钥暴露给客户端

    **支持的参数：**
    - `model`: 模型显示名称（必须是应用有权访问的模型）
    - `messages`: 对话消息列表
    - `temperature`: 温度参数 (0-2)
    - `top_p`: top_p参数 (0-1)
    - `max_tokens`: 最大生成token数
    - `stream`: 是否启用流式响应
    - `stop`: 停止词
    - `presence_penalty`: 存在惩罚 (-2 to 2)
    - `frequency_penalty`: 频率惩罚 (-2 to 2)

    **响应模式：**
    - **非流式**: 返回完整的JSON响应
    - **流式**: 返回Server-Sent Events (SSE)流

    **错误处理：**
    - 严格按照OpenAI API错误格式返回
    - 包含详细的错误类型和消息

    **使用场景：**
    - 聊天机器人对话
    - 文本生成和补全
    - 代码生成和解释
    - 翻译和总结任务
    """,
    response_description="聊天完成响应，支持流式和非流式格式",
    tags=["聊天对话"]
)
async def create_chat_completion(
    request_data: ChatCompletionRequest,
    request: Request,
    application: Application = Depends(get_current_application),
    db: Session = Depends(get_db)
):
    """
    创建聊天完成请求

    处理聊天完成请求，支持流式和非流式响应。
    实现安全的代理机制：
    1. 根据API密钥验证应用身份
    2. 获取应用可用的模型列表
    3. 按模型显示名称（display_name）查找模型
    4. 使用模型的内部名称（internal_name）和关联平台的凭据
    5. 代理请求到真实的LLM服务商API
    6. 避免将服务商API密钥暴露给客户端

    Args:
        request_data: 聊天完成请求数据（model字段为显示名称）
        request: FastAPI请求对象（用于获取客户端IP）
        application: 通过中间件验证的应用对象
        db: 数据库会话

    Returns:
        Union[dict, StreamingResponse]:
            - 非流式：JSON格式的完成响应
            - 流式：Server-Sent Events流响应

    Raises:
        HTTPException: 401 - API密钥无效
        HTTPException: 404 - 模型显示名称不存在或应用无权访问
        HTTPException: 500 - 服务器内部错误
    """
    try:
        # 获取客户端IP
        client_ip = request.client.host if request.client else "unknown"

        # 转换请求数据为字典格式，排除None值
        request_dict = request_data.dict(exclude_none=True)

        if request_data.stream:
            # 流式响应
            async def generate_stream():
                try:
                    async for chunk in ChatService.stream_chat_completion(
                        request_dict, application, client_ip
                    ):
                        yield f"data: {json.dumps(chunk)}\n\n"
                    yield "data: [DONE]\n\n"
                except Exception as e:
                    logger.error(f"Stream error: {e}")
                    error_chunk = {
                        "error": {
                            "message": str(e),
                            "type": "internal_error"
                        }
                    }
                    yield f"data: {json.dumps(error_chunk)}\n\n"

            return StreamingResponse(
                generate_stream(),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/plain; charset=utf-8"
                }
            )
        else:
            # 非流式响应
            response = await ChatService.create_chat_completion(
                request_dict, application, client_ip
            )
            return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chat completion error: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": {
                    "message": "Internal server error",
                    "type": "internal_error"
                }
            }
        )
