# 平台配置修复说明

本文档说明了对模型管理系统的三个主要修复：

## 🔧 修复内容

### 1. 修复 platform_init.py 中的重复代码

**问题**: `platform_init.py` 文件中存在重复的平台配置定义

**解决方案**:
- 提取平台配置为全局常量 `PLATFORM_CONFIGS`
- 重构 `get_platform_configs_from_env()` 和 `get_platform_env_status()` 方法使用统一配置
- 消除代码重复，提高维护性

### 2. 修复数据库表已存在错误

**问题**: Docker 重启时出现 "Table 'platforms' already exists" 错误

**解决方案**:
- 在 `app.py` 中添加异常处理
- 忽略表已存在的错误（这在容器重启时是正常的）
- 保留其他数据库错误的抛出

### 3. 完善环境变量配置

**问题**: `.env` 文件中缺少平台相关的环境变量

**解决方案**:
- 在 `.env` 文件中添加所有平台的环境变量配置
- 在 `docker-compose.yml` 中将环境变量传递给 model-manager 服务
- 创建 `.env.example` 示例文件

## 📋 新增的环境变量

### 功能开关
```bash
INIT_PLATFORMS_ENABLED=true  # 是否启用平台自动初始化
```

### 平台配置
每个平台需要三个环境变量：
- `{PLATFORM}_PLATFORM_NAME`: 平台显示名称
- `{PLATFORM}_BASE_URL`: API 基础URL
- `{PLATFORM}_API_KEY`: API 密钥

支持的平台：
- **DashScope** (阿里云)
- **DeepSeek**
- **GLM** (智谱AI)
- **Spark** (讯飞星火)
- **OpenRouter**
- **DeepBricks**

## 🚀 使用方法

### 1. 配置环境变量

复制示例文件并修改：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的 API 密钥：
```bash
# 示例：配置 DeepSeek
DEEPSEEK_PLATFORM_NAME=DeepSeek
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_API_KEY=sk-your_actual_api_key_here
```

### 2. 重启服务

```bash
# 重启 model-manager 服务以加载新配置
docker-compose restart model-manager

# 或者重启所有服务
docker-compose down && docker-compose up -d
```

### 3. 验证配置

访问管理界面检查平台是否正确初始化：
- 管理界面: http://localhost:20000
- 登录后查看平台管理页面

## ⚠️ 注意事项

1. **API 密钥安全**: 请妥善保管 API 密钥，不要提交到版本控制系统
2. **选择性配置**: 只配置你需要使用的平台，未配置的平台会被跳过
3. **配置验证**: 系统会验证环境变量的完整性，缺少任何一个变量的平台将被跳过
4. **日志查看**: 可通过 `docker-compose logs model-manager` 查看平台初始化日志

## 🔍 故障排除

### 如果平台没有正确初始化：

1. 检查环境变量是否正确设置：
```bash
docker-compose exec model-manager env | grep -E "(DASHSCOPE|DEEPSEEK|GLM|SPARK|OPENROUTER|DEEPBRICKS)"
```

2. 查看初始化日志：
```bash
docker-compose logs model-manager | grep -i platform
```

3. 确认 `INIT_PLATFORMS_ENABLED=true`

### 如果仍然出现数据库错误：

1. 清理数据库数据（注意：会丢失所有数据）：
```bash
docker-compose down -v
docker-compose up -d
```

2. 或者手动连接数据库检查表结构：
```bash
docker-compose exec models_db mysql -u models_user -p vdb_models
```

## 📝 更新日志

- 修复了 platform_init.py 中的代码重复问题
- 解决了容器重启时的数据库表冲突错误
- 完善了环境变量配置和文档
- 添加了平台配置的统一管理机制
